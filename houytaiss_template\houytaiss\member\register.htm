<!--{template common/header}-->

<!-- 手机端容器 -->
<div class="mobile-container">
    <!-- 注册页面 -->
    <div class="register-page">
        <div class="register-title">用户注册</div>
        
        <form method="post" action="member.php?mod=register&regsubmit=yes&infloat=yes&handlekey=register&inajax=1" onsubmit="return regSubmit()">
            <input type="hidden" name="formhash" value="{FORMHASH}" />
            <input type="hidden" name="referer" value="{dreferer($referer)}" />
            
            <div class="register-form">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" name="username" class="form-control" placeholder="请输入用户名" required>
                    <div class="form-hint">用户名长度为3-15个字符</div>
                </div>
                
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" name="password" class="form-control" placeholder="请输入密码" required>
                    <div class="form-hint">密码长度不少于6个字符</div>
                </div>
                
                <div class="form-group">
                    <label for="password2">确认密码:</label>
                    <input type="password" id="password2" name="password2" class="form-control" placeholder="请再次输入密码" required>
                </div>
                
                <div class="form-group">
                    <label for="email">邮箱:</label>
                    <input type="email" id="email" name="email" class="form-control" placeholder="请输入邮箱地址" required>
                </div>
                
                <!--{if $_G['setting']['seccodestatus'] & 2}-->
                <div class="form-group">
                    <label for="seccode">验证码:</label>
                    <div class="seccode-group">
                        <input type="text" id="seccode" name="seccodeverify" class="form-control seccode-input" placeholder="验证码" required>
                        <img src="misc.php?mod=seccode&action=update&idhash={$_G['setting']['seccodedata']['idhash']}&{time()}" class="seccode-img" onclick="this.src=this.src+'&refresh='+Math.random();" title="点击更换验证码">
                    </div>
                </div>
                <!--{/if}-->
                
                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" name="agreement" value="1" required> 我已阅读并同意<a href="#" target="_blank">用户协议</a>
                    </label>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn-register">立即注册</button>
                </div>
                
                <div class="form-links">
                    <a href="member.php?mod=logging&action=login">已有账号？立即登录</a>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
/* 手机端容器 */
.mobile-container {
    width: 375px;
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    position: relative;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 注册页面 */
.register-page {
    width: 300px;
    background: white;
    padding: 30px;
}

.register-title {
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 25px;
    color: #333;
}

.register-form {
    width: 100%;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 5px rgba(40, 167, 69, 0.3);
}

.form-hint {
    font-size: 10px;
    color: #666;
    margin-top: 3px;
}

.seccode-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.seccode-input {
    flex: 1;
}

.seccode-img {
    height: 36px;
    cursor: pointer;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    font-size: 12px;
    margin-bottom: 0;
    cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 5px;
}

.checkbox-group a {
    color: #007bff;
    text-decoration: none;
    margin-left: 3px;
}

.checkbox-group a:hover {
    text-decoration: underline;
}

.btn-register {
    width: 100%;
    padding: 10px;
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    outline: none;
}

.btn-register:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.form-links {
    margin-top: 15px;
    text-align: center;
}

.form-links a {
    display: block;
    font-size: 12px;
    color: #007bff;
    text-decoration: none;
    margin-bottom: 5px;
}

.form-links a:hover {
    text-decoration: underline;
}

/* 真正的手机端样式 */
@media (max-width: 767px) {
    body {
        display: block;
        background: white;
    }

    .mobile-container {
        width: 100%;
        box-shadow: none;
    }

    .register-page {
        width: 90%;
        max-width: 300px;
    }
}
</style>

<script>
function regSubmit() {
    var username = document.getElementById('username').value;
    var password = document.getElementById('password').value;
    var password2 = document.getElementById('password2').value;
    var email = document.getElementById('email').value;
    var agreement = document.querySelector('input[name="agreement"]').checked;
    
    if (!username) {
        alert('请输入用户名');
        return false;
    }
    
    if (username.length < 3 || username.length > 15) {
        alert('用户名长度应为3-15个字符');
        return false;
    }
    
    if (!password) {
        alert('请输入密码');
        return false;
    }
    
    if (password.length < 6) {
        alert('密码长度不能少于6个字符');
        return false;
    }
    
    if (password !== password2) {
        alert('两次输入的密码不一致');
        return false;
    }
    
    if (!email) {
        alert('请输入邮箱地址');
        return false;
    }
    
    if (!agreement) {
        alert('请先阅读并同意用户协议');
        return false;
    }
    
    return true;
}
</script>

<!--{template common/footer}-->
