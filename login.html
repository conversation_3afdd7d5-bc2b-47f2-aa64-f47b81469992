<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 375px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 登录页面 */
        .login-page {
            width: 300px;
            background: white;
            padding: 30px;
        }

        .login-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 25px;
            color: #333;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .login-input {
            padding: 8px 10px;
            border: 1px solid #ccc;
            font-size: 14px;
            outline: none;
        }

        .login-input:focus {
            border-color: #007bff;
        }

        .login-submit {
            padding: 10px 20px;
            background: #ffc107;
            color: #333;
            border: none;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 10px;
        }

        .login-submit:hover {
            background: #e0a800;
        }

        .register-link {
            text-align: center;
            margin-top: 15px;
        }

        .register-link a {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }

        .register-link a:hover {
            text-decoration: underline;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        /* 真正的手机端样式 */
        @media (max-width: 767px) {
            body {
                display: block;
                background: white;
            }
            
            .mobile-container {
                width: 100%;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <!-- 手机端容器 -->
    <div class="mobile-container">
        <!-- 登录页面 -->
        <div class="login-page">
            <div class="login-title">用户登录</div>
            <form class="login-form" onsubmit="submitLogin(event)">
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" class="login-input" required>
                </div>
                <div class="form-group">
                    <label>密&nbsp;&nbsp;&nbsp;&nbsp;码</label>
                    <input type="password" class="login-input" required>
                </div>
                <button type="submit" class="login-submit">登录</button>
            </form>
            <div class="register-link">
                <a href="register.html">马上注册</a>
            </div>
            <div class="back-link">
                <a href="index.html">返回首页</a>
            </div>
        </div>
    </div>

    <script>
        function submitLogin(event) {
            event.preventDefault();
            const inputs = document.querySelectorAll('.login-input');
            const username = inputs[0].value;
            const password = inputs[1].value;
            
            if (username && password) {
                alert('登录成功：用户名 ' + username);
                // 可以在这里添加跳转到首页的逻辑
                window.location.href = 'index.html';
            } else {
                alert('请输入用户名和密码');
            }
        }
    </script>
</body>
</html>
