<!--{eval $_G['setting']['styleid'] = STYLEID;}-->
<!--{eval $_G['setting']['tplrefresh'] = 1;}-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><!--{if $_G['setting']['bbname']}-->{$_G['setting']['bbname']}<!--{/if}--><!--{if $navtitle}--> - $navtitle<!--{/if}--></title>
    <meta name="keywords" content="$_G[setting][bbname]" />
    <meta name="description" content="$_G[setting][bbname]" />
    <meta name="generator" content="Discuz! {DISCUZ_VERSION}" />
    <meta name="author" content="Discuz! Team and Comsenz UI Team" />
    <meta name="copyright" content="2001-{date('Y', TIMESTAMP)} Comsenz Inc." />
    <meta name="MSSmartTagsPreventParsing" content="True" />
    <meta http-equiv="MSThemeCompatible" content="Yes" />
    <link rel="stylesheet" type="text/css" href="{STATICURL}css/houytaiss.css?{VERHASH}" />
    <!--[if IE]><link rel="stylesheet" type="text/css" href="{STATICURL}css/houytaiss_ie.css?{VERHASH}" /><![endif]-->
    <script type="text/javascript">var STYLEID = '{STYLEID}', STATICURL = '{STATICURL}', IMGDIR = '{IMGDIR}', VERHASH = '{VERHASH}', charset = '{CHARSET}', discuz_uid = '$_G[uid]', cookiepre = '{$_G[config][cookie][cookiepre]}', cookiedomain = '{$_G[config][cookie][cookiedomain]}', cookiepath = '{$_G[config][cookie][cookiepath]}', showusercard = '{$_G[setting][showusercard]}', attackevasive = '{$_G[config][security][attackevasive]}', disallowfloat = '{$_G[setting][disallowfloat]}', creditnotice = '<!--{if $_G[setting][creditnotice]}-->$_G[setting][creditnotice]<!--{/if}-->', defaultstyle = '$_G[style][defaultextstyle]', REPORTURL = '$_G[currenturl_encode]', SITEURL = '$_G[siteurl]', JSPATH = '$_G[setting][jspath]', CSSPATH = '$_G[setting][csspath]', DYNAMICURL = '$_G[dynamicurl]';</script>
    <script src="{$_G[setting][jspath]}common.js?{VERHASH}" type="text/javascript"></script>
    <!--{if $_G['setting']['pluginhtstatus']}-->
    <script src="{$_G[setting][jspath]}ht.js?{VERHASH}" type="text/javascript"></script>
    <!--{/if}-->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 375px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        /* 顶部图片区域 */
        .hero-section {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding-top: 0;
        }

        /* 标签区域 */
        .tabs-section {
            width: 100%;
            padding: 5px 0;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
        }

        .tabs-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 5px;
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 2px 8px;
            margin: 0 2px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(153, 153, 153, 0.8);
            color: white;
            border: none;
            outline: none;
            line-height: 1.2;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* 未点击状态 - 灰色毛玻璃 */
        .tab-item:not(.active) {
            background: rgba(153, 153, 153, 0.8);
            color: white;
        }

        /* 点击状态颜色 - 毛玻璃效果 */
        .tab-item.active.tab-1 {
            background: rgba(40, 167, 69, 0.8); /* 绿色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-2 {
            background: rgba(0, 123, 255, 0.8); /* 蓝色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-3 {
            background: rgba(220, 53, 69, 0.8); /* 红色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-4 {
            background: rgba(52, 58, 64, 0.8); /* 黑色毛玻璃 */
            color: white;
        }

        /* 悬停效果 */
        .tab-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
        }

        /* 开奖信息区域 */
        .lottery-info {
            width: 100%;
            padding: 5px 15px;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .lottery-text {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }

        .period-number {
            color: #dc3545;
            font-weight: 600;
        }

        /* 开奖记录按钮 */
        .record-btn {
            padding: 4px 12px;
            border: none;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #8B5CF6 0%, #FFFFFF 100%);
            color: #333;
            outline: none;
            box-shadow: 0 2px 6px rgba(139, 92, 246, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        .record-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 10px rgba(139, 92, 246, 0.4);
        }

        /* 真正的手机端样式 */
        @media (max-width: 767px) {
            body {
                display: block;
                background: white;
            }

            .mobile-container {
                width: 100%;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
