    <!-- 右下角浮动菜单 -->
    <div class="floating-menu">
        <a href="member.php?mod=register" class="menu-item">注册</a>
        <a href="javascript:void(0)" class="menu-item" onclick="openRecharge()">充币</a>
        <a href="promotion.html" class="menu-item">优惠</a>
        <a href="about.html" class="menu-item">介绍</a>
    </div>

    <!-- 充币弹窗遮罩 -->
    <div class="recharge-overlay" id="rechargeOverlay">
        <div class="recharge-modal">
            <span class="close-recharge" onclick="closeRecharge()">&times;</span>
            <img src="{IMGDIR}/11.png" alt="充币二维码" class="recharge-qr">
            <div class="recharge-title">扫码联系客服</div>
        </div>
    </div>

    <style>
        /* 右下角浮动菜单 */
        .floating-menu {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .menu-item {
            width: 45px;
            height: 45px;
            background: #000000cc;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 10px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 2px 7px rgba(0, 0, 0, 0.3);
        }

        /* 充币弹窗遮罩 */
        .recharge-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: flex-end;
            z-index: 2000;
        }

        /* 充币弹窗 */
        .recharge-modal {
            width: 100%;
            background: white;
            border-radius: 12px 12px 0 0;
            padding: 20px;
            text-align: center;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .recharge-modal.show {
            transform: translateY(0);
        }

        .close-recharge {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }

        .recharge-qr {
            width: 200px;
            height: auto;
            margin: 10px 0 15px 0;
        }

        .recharge-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-top: 10px;
        }
    </style>

    <script>
        function openRecharge() {
            const overlay = document.getElementById('rechargeOverlay');
            const modal = overlay.querySelector('.recharge-modal');
            overlay.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);

            // 防止页面滚动
            document.body.style.overflow = 'hidden';
        }

        function closeRecharge() {
            const overlay = document.getElementById('rechargeOverlay');
            const modal = overlay.querySelector('.recharge-modal');
            modal.classList.remove('show');
            setTimeout(() => {
                overlay.style.display = 'none';
                // 恢复页面滚动
                document.body.style.overflow = '';
            }, 300);
        }

        // 点击空白区域关闭弹窗
        document.addEventListener('DOMContentLoaded', function() {
            const overlay = document.getElementById('rechargeOverlay');
            if (overlay) {
                overlay.addEventListener('click', function(e) {
                    if (e.target === overlay) {
                        closeRecharge();
                    }
                });
            }
        });
    </script>

<!--{if !empty($_G['setting']['footernavs'])}-->
<div class="wp">
    <div class="footernavs cl">
        <!--{loop $_G['setting']['footernavs'] $nav}-->
        <a href="$nav[url]" <!--{if $nav['type']}-->target="_blank"<!--{/if}-->>$nav[name]</a>
        <!--{/loop}-->
    </div>
</div>
<!--{/if}-->

<!--{if $_G['setting']['stat']}-->
<div id="footer">
    <div class="wp">
        <div id="footlinks">
            <p>
                <strong><a href="$_G[siteurl]" target="_blank">$_G[setting][bbname]</a></strong>
                <!--{if $_G[setting][icp]}-->- <a href="http://www.miitbeian.gov.cn/" target="_blank">$_G[setting][icp]</a><!--{/if}-->
            </p>
            <p class="smalltext">
                Powered by <strong><a href="http://www.discuz.net" target="_blank">Discuz!</a></strong> <em>$_G[setting][version]</em>
                &copy; 2001-{date('Y', TIMESTAMP)} <a href="http://www.comsenz.com" target="_blank">Comsenz Inc.</a>
            </p>
        </div>
    </div>
</div>
<!--{/if}-->

</body>
</html>
