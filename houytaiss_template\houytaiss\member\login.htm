<!--{template common/header}-->

<!-- 手机端容器 -->
<div class="mobile-container">
    <!-- 登录页面 -->
    <div class="login-page">
        <div class="login-title">用户登录</div>
        
        <form method="post" action="member.php?mod=logging&action=login&loginsubmit=yes&handlekey=login&loginhash={$loginhash}&inajax=1" onsubmit="return lsSubmit()">
            <input type="hidden" name="formhash" value="{FORMHASH}" />
            <input type="hidden" name="referer" value="{dreferer($referer)}" />
            
            <div class="login-form">
                <div class="form-group">
                    <label for="username">用户名/邮箱:</label>
                    <input type="text" id="username" name="username" class="form-control" placeholder="请输入用户名或邮箱" required>
                </div>
                
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" name="password" class="form-control" placeholder="请输入密码" required>
                </div>
                
                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" name="cookietime" value="2592000" checked> 记住我
                    </label>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn-login">登录</button>
                </div>
                
                <div class="form-links">
                    <a href="member.php?mod=register">还没有账号？立即注册</a>
                    <a href="member.php?mod=getpasswd">忘记密码？</a>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
/* 手机端容器 */
.mobile-container {
    width: 375px;
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    position: relative;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 登录页面 */
.login-page {
    width: 300px;
    background: white;
    padding: 30px;
}

.login-title {
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 25px;
    color: #333;
}

.login-form {
    width: 100%;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
}

.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    font-size: 12px;
    margin-bottom: 0;
    cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 5px;
}

.btn-login {
    width: 100%;
    padding: 10px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    outline: none;
}

.btn-login:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.form-links {
    margin-top: 15px;
    text-align: center;
}

.form-links a {
    display: block;
    font-size: 12px;
    color: #007bff;
    text-decoration: none;
    margin-bottom: 5px;
}

.form-links a:hover {
    text-decoration: underline;
}

/* 真正的手机端样式 */
@media (max-width: 767px) {
    body {
        display: block;
        background: white;
    }

    .mobile-container {
        width: 100%;
        box-shadow: none;
    }

    .login-page {
        width: 90%;
        max-width: 300px;
    }
}
</style>

<script>
function lsSubmit() {
    var username = document.getElementById('username').value;
    var password = document.getElementById('password').value;
    
    if (!username) {
        alert('请输入用户名或邮箱');
        return false;
    }
    
    if (!password) {
        alert('请输入密码');
        return false;
    }
    
    return true;
}
</script>

<!--{template common/footer}-->
